<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <title th:text="${pageTitle}">视频管理</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/admin-style.css" rel="stylesheet">

    <!-- 可点击状态样式 -->
    <style>
        .clickable-status {
            transition: all 0.2s ease;
            user-select: none;
        }

        .clickable-status:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .clickable-status:active {
            transform: scale(0.95);
        }

        .clickable-status.bg-success:hover {
            background-color: #198754 !important;
        }

        .clickable-status.bg-secondary:hover {
            background-color: #6c757d !important;
        }

        /* ID折叠样式 */
        .id-container {
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .id-collapsed {
            max-width: 50px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .id-expanded {
            max-width: none;
            word-break: break-all;
        }

        .id-toggle-icon {
            font-size: 0.8em;
            margin-left: 4px;
            transition: transform 0.3s ease;
        }

        .id-expanded .id-toggle-icon {
            transform: rotate(180deg);
        }

        .id-container:hover {
            background-color: #e9ecef !important;
            transform: scale(1.02);
        }

        .id-full-text {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">


    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center text-white">
            <div class="spinner-border mb-3" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div>正在处理，请稍候...</div>
        </div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top" style="background-color: #0d6efd !important; color-scheme: light only !important;">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 快速操作 -->
                <div class="d-flex gap-1">
                    <a href="/admin/add" class="btn btn-success btn-sm">
                        <i class="fas fa-plus me-1"></i>添加
                    </a>
                    <button class="btn btn-outline-light btn-sm" onclick="refreshPage()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid px-4">
        <!-- 搜索和筛选区域 -->
            <div class="search-section">
                <div class="row g-3 align-items-end">
                    <div class="col-lg-4 col-md-6">
                        <label for="adminSearch" class="form-label fw-semibold">
                            <i class="fas fa-search me-1"></i>搜索视频
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="adminSearch"
                                   placeholder="输入标题或描述关键词..." th:value="${param.search}">
                            <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <!-- <div class="form-text">支持模糊搜索，实时过滤结果</div> -->
                    </div>

                    <div class="col-lg-2 col-md-3">
                        <label for="statusFilter" class="form-label fw-semibold">状态</label>
                        <select class="form-select" id="statusFilter" th:value="${param.status}">
                            <option value="">全部状态</option>
                            <option value="active">✅ 启用中</option>
                            <option value="inactive">⏸️ 已禁用</option>
                        </select>
                    </div>





                    <div class="col-lg-2 col-md-6">
                        <div class="d-grid gap-2 d-md-flex">
                            <button class="btn btn-primary" onclick="applyFilters()">
                                <i class="fas fa-filter me-1"></i>筛选
                            </button>
                            <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                        </div>
                    </div>

                </div>
            </div>

        <!-- 操作工具栏 -->
            <div class="d-flex justify-content-between align-items-center mb-4 flex-column flex-md-row">
                <div class="d-flex align-items-center gap-3 mb-3 mb-md-0 w-100 w-md-auto justify-content-start">
                    <h3 class="mb-0 fw-bold">
                        <i class="fas fa-list me-2 text-primary"></i>视频列表
                    </h3>
                    <span class="badge bg-secondary" id="totalVideosCount">
                        共 <span th:text="${videos != null ? videos.size() : 0}">0</span> 个视频
                    </span>
                    <span class="badge bg-info" id="filteredCount" style="display: none;">
                        筛选后 <span>0</span> 个
                    </span>
                </div>

                <div class="d-flex gap-2 flex-wrap justify-content-start justify-content-md-end w-100 w-md-auto">
                    <!-- ID显示控制 -->
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-info btn-sm" onclick="expandAllIds()" title="展开所有ID">
                            <i class="fas fa-expand-alt me-1"></i>展开ID
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="collapseAllIds()" title="折叠所有ID">
                            <i class="fas fa-compress-alt me-1"></i>折叠ID
                        </button>
                    </div>

                    <!-- 批量操作 -->
                    <div class="btn-group" role="group">
                        <input type="checkbox" class="btn-check" id="selectAllTable" autocomplete="off">
                        <label class="btn btn-outline-primary" for="selectAllTable">
                            <i class="fas fa-check-square me-1"></i>全选
                        </label>

                        <button class="btn btn-outline-success batch-operation" disabled onclick="batchEnable()" title="批量启用">
                            <i class="fas fa-play me-1"></i>启用
                        </button>
                        <button class="btn btn-outline-warning batch-operation" disabled onclick="batchDisable()" title="批量禁用">
                            <i class="fas fa-pause me-1"></i>禁用
                        </button>
                        <button class="btn btn-outline-danger batch-operation" disabled onclick="batchDelete()" title="批量删除">
                            <i class="fas fa-trash me-1"></i>删除
                        </button>
                    </div>


                </div>
            </div>

        <!-- 视频列表 -->
        <div class="table-container">
            <!-- 空状态提示 -->
            <div th:if="${videos == null or videos.isEmpty()}" class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-video fa-4x text-muted"></i>
                </div>
                <h4 class="text-muted">暂无视频</h4>
                <p class="text-muted mb-4">还没有上传任何视频！</p>
                <a href="/admin/add" class="btn btn-primary btn-lg">
                    <i class="fas fa-plus me-2"></i>添加视频
                </a>
            </div>

            <!-- 视频表格 -->
            <div th:if="${videos != null and !videos.isEmpty()}">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="videosTable">
                        <thead class="table-light sticky-top">
                            <tr>
                                <th style="width: 10px;padding: 8px 0px 8px 8px;" class="text-center">
                                    <div class="form-check" style="padding: 0px 0px 0px 26px;margin: 0px;">
                                        <input type="checkbox" class="form-check-input" id="selectAllTable">
                                        <label class="form-check-label" for="selectAllTable"></label>
                                    </div>
                                </th>
                                <th style="width: 10px;padding: 8px 2px;" class="text-start">ID</th>
                                <th style="width: 20px;padding: 8px 2px;" class="text-center">缩略图</th>
                                <th style="width: 40px; padding: 8px 4px;">详情</th>
                                <th style="width: 10px;padding: 8px 2px;" class="text-center">状态</th>
                                <th style="width: 50px;" class="text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="video : ${videos}"
                                th:class="${video.isActive ? 'video-row' : 'video-row disabled'}"
                                th:data-video-id="${video.id}">

                                <!-- 选择框 -->
                                <td class="text-center">
                                    <div class="form-check" style="padding: 0px 0px 0px 26px;">
                                        <input type="checkbox" class="form-check-input video-checkbox"
                                               th:value="${video.id}" th:id="'check-' + ${video.id}">
                                        <label class="form-check-label" th:for="'check-' + ${video.id}"></label>
                                    </div>
                                </td>

                                <!-- ID -->
                                <td class="text-start" style="padding: 8px 0px;">
                                    <div class="id-container badge bg-light text-dark fw-bold id-collapsed"
                                         th:data-full-id="${video.id}"
                                         onclick="toggleIdDisplay(this)"
                                         ondblclick="copyVideoId(this.dataset.fullId)"
                                         title="单击展开/折叠，双击复制ID">
                                        <span class="id-display-text" th:text="${#strings.substring(video.id, 0, 4) + '...'}">ABC1...</span>
                                        <i class="fas fa-chevron-down id-toggle-icon"></i>
                                        <div class="id-full-text" style="display: none;" th:text="${video.id}">完整ID</div>
                                    </div>
                                </td>

                                <!-- 缩略图 -->
                                <td class="text-center" style="padding: 8px 2px;">
                                    <div class="position-relative">
                                        <img th:src="${video.thumbnailUrl != null ? video.thumbnailUrl : 'https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png'}"
                                             class="video-thumbnail shadow-sm thumbnail-optimized"
                                             th:alt="${video.title}"
                                             loading="lazy"
                                             decoding="async"
                                             onerror="this.src='https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png'"
                                             onload="this.classList.add('loaded')"
                                             data-loaded="false">
                                    </div>
                                </td>

                                <!-- 视频信息 -->
                                <td style="padding: 8px 2px;">
                                    <div class="d-flex flex-column">
                                        <h6 class="video-title mb-1" th:text="${video.title}">视频标题</h6>
                                        <p class="video-description mb-2"
                                           th:if="${video.description != null and !video.description.isEmpty()}"
                                           th:text="${video.description}">视频描述内容...</p>


                                    </div>
                                </td>

                                <!-- 状态 -->
                                <td class="text-center status-column" style="padding: 8px 2px;">
                                    <div class="d-flex flex-column align-items-center gap-1">
                                        <!-- 可点击的状态标识 -->
                                        <span class="status-badge badge bg-success clickable-status"
                                              th:if="${video.isActive}"
                                              th:data-video-id="${video.id}"
                                              th:data-is-active="${video.isActive}"
                                              onclick="toggleVideoStatus(this.dataset.videoId, this.dataset.isActive === 'true')"
                                              title="点击切换为禁用状态"
                                              style="cursor: pointer;">
                                            <i class="fas fa-check me-1"></i>启用
                                        </span>
                                        <span class="status-badge badge bg-secondary clickable-status"
                                              th:unless="${video.isActive}"
                                              th:data-video-id="${video.id}"
                                              th:data-is-active="${video.isActive}"
                                              onclick="toggleVideoStatus(this.dataset.videoId, this.dataset.isActive === 'true')"
                                              title="点击切换为启用状态"
                                              style="cursor: pointer;">
                                            <i class="fas fa-pause me-1"></i>禁用
                                        </span>

                                        <!-- 置顶标识 - 放在状态下方，可点击切换 -->
                                        <span class="badge bg-warning text-dark clickable-pin-status"
                                              th:if="${video.isPinned != null and video.isPinned}"
                                              th:data-video-id="${video.id}"
                                              th:data-video-title="${video.title}"
                                              th:data-is-pinned="${video.isPinned}"
                                              onclick="toggleVideoPin(this.dataset.videoId, this.dataset.isPinned === 'true', this.dataset.videoTitle)"
                                              title="点击取消置顶"
                                              style="cursor: pointer;">
                                            <i class="fas fa-thumbtack me-1"></i>置顶
                                        </span>

                                        <!-- 未置顶状态 - 可点击设置置顶 -->
                                        <span class="badge bg-light text-dark border clickable-pin-status"
                                              th:if="${video.isPinned == null or !video.isPinned}"
                                              th:data-video-id="${video.id}"
                                              th:data-video-title="${video.title}"
                                              th:data-is-pinned="${video.isPinned != null and video.isPinned}"
                                              onclick="toggleVideoPin(this.dataset.videoId, this.dataset.isPinned === 'true', this.dataset.videoTitle)"
                                              title="点击设置为置顶"
                                              style="cursor: pointer;">
                                            <i class="fas fa-thumbtack me-1"></i>未置顶
                                        </span>
                                    </div>
                                </td>
                                <!-- 操作 -->
                                <td class="text-center" style="padding: 8px 3px 8px 4px;">
                                    <div class="d-flex justify-content-center gap-1 flex-wrap">
                                        <!-- 主要操作 -->
                                        <a th:href="@{/play/{id}(id=${video.id})}"
                                           class="btn btn-sm btn-outline-primary action-btn"
                                           title="播放视频" target="_blank">
                                            <i class="fas fa-play"></i>
                                        </a>

                                        <a th:href="@{/admin/edit/{id}(id=${video.id})}"
                                           class="btn btn-sm btn-outline-warning action-btn"
                                           title="编辑视频">
                                            <i class="fas fa-edit"></i>
                                        </a>


                                        <!-- 删除按钮 - 只有禁用状态的视频才能删除 -->
                                        <button class="btn btn-sm btn-outline-danger action-btn delete-btn"
                                                th:data-video-id="${video.id}"
                                                th:data-video-title="${video.title}"
                                                onclick="confirmDeleteVideo(this.dataset.videoId, this.dataset.videoTitle)"
                                                title="永久删除视频"
                                                th:style="${video.isActive ? 'display: none;' : 'display: inline-block;'}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 表格底部信息 -->
                <div class="d-flex justify-content-between align-items-center p-3 bg-light border-top">
                    <div class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        共显示 <span class="fw-bold" th:text="${videos.size()}">0</span> 个视频
                    </div>
                    <div class="d-flex gap-2 align-items-center">
                        <span id="selectedInfo" class="text-muted" style="display: none; margin-right: 3px;">
                            已选<span class="fw-bold text-primary">0</span>
                        </span>
                        <button class="btn btn-sm btn-outline-secondary" id="toggleSelectAllBtn" onclick="toggleSelectAll()">
                            <i class="fas fa-check-square me-1" id="toggleSelectAllIcon"></i><span id="toggleSelectAllText">全选</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>


    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="footer-single-container">
            <p class="spqk01">
                <span>轻康自然，享瘦生活。</span>
            </p>
            <p  class="spqk02">
                <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">点这里：联系我们</a></small>
            </p>
        </div>
    </footer>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2"></i>确认删除
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <i class="fas fa-trash fa-3x text-danger"></i>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>注意：</strong>此操作不可撤销，删除后无法恢复。
                    </div>
                    <div class="bg-light p-3 rounded">
                        <strong>视频标题：</strong><span id="deleteVideoTitle" class="text-primary"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="fas fa-trash me-1"></i>确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-question-circle me-2"></i>使用帮助
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-search me-2 text-primary"></i>搜索功能</h6>
                            <ul class="list-unstyled mb-4">
                                <li>• 支持标题和描述模糊搜索</li>
                                <li>• 实时过滤，无需点击搜索按钮</li>
                                <li>• 可按状态和格式筛选</li>
                            </ul>

                            <h6><i class="fas fa-check-square me-2 text-success"></i>批量操作</h6>
                            <ul class="list-unstyled">
                                <li>• 勾选视频后可批量启用/禁用</li>
                                <li>• 支持批量删除操作</li>
                                <li>• 全选/反选功能</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-cog me-2 text-warning"></i>视频管理</h6>
                            <ul class="list-unstyled mb-4">
                                <li>• 点击播放按钮预览视频</li>
                                <li>• 编辑按钮修改视频信息</li>
                                <li>• 状态切换启用/禁用视频</li>
                                <li>• 单击ID展开/折叠显示</li>
                                <li>• 双击ID快速复制到剪贴板</li>
                            </ul>

                            <h6><i class="fas fa-keyboard me-2 text-info"></i>快捷键</h6>
                            <ul class="list-unstyled">
                                <li>• <kbd>Ctrl+A</kbd>：全选视频</li>
                                <li>• <kbd>Delete</kbd>：删除选中视频</li>
                                <li>• <kbd>F5</kbd>：刷新页面</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                        <i class="fas fa-check me-1"></i>我知道了
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/admin.js"></script>
</body>
</html>

